-- Admin User Setup for Palm Beach Lakes Alumni Association
-- Run these commands in your Supabase SQL editor to fix image upload issues

-- First, create the admin user account
-- Note: You'll need to create this user through the Supabase Auth interface first
-- Go to Authentication > Users in your Supabase dashboard and create a user with:
-- Email: <EMAIL>
-- Password: Rams1989!
-- Then run this SQL to set the admin role:

-- Update the admin user with admin role metadata
-- Replace 'USER_ID_HERE' with the actual UUID of the admin user from the auth.users table
UPDATE auth.users 
SET raw_user_meta_data = jsonb_set(
  COALESCE(raw_user_meta_data, '{}'),
  '{role}',
  '"admin"'
)
WHERE email = '<EMAIL>';

-- Alternative: If you want to find and update the admin user automatically:
-- UPDATE auth.users 
-- SET raw_user_meta_data = jsonb_set(
--   COALESCE(raw_user_meta_data, '{}'),
--   '{role}',
--   '"admin"'
-- )
-- WHERE email = '<EMAIL>';

-- Update storage policies to be more permissive for admin operations
-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Admins can upload event images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can update event images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete event images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload gallery images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can update gallery images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete gallery images" ON storage.objects;

-- Create more permissive policies for admin operations
-- Event images policies
CREATE POLICY "Admin can upload event images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'event-images' AND
    auth.role() = 'authenticated' AND
    (
      -- Allow admin user
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.email = '<EMAIL>'
      )
      OR
      -- Allow users with admin role metadata
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_user_meta_data->>'role' = 'admin'
      )
    )
  );

CREATE POLICY "Admin can update event images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'event-images' AND
    auth.role() = 'authenticated' AND
    (
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.email = '<EMAIL>'
      )
      OR
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_user_meta_data->>'role' = 'admin'
      )
    )
  );

CREATE POLICY "Admin can delete event images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'event-images' AND
    auth.role() = 'authenticated' AND
    (
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.email = '<EMAIL>'
      )
      OR
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_user_meta_data->>'role' = 'admin'
      )
    )
  );

-- Gallery images policies
CREATE POLICY "Admin can upload gallery images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'gallery-images' AND
    auth.role() = 'authenticated' AND
    (
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.email = '<EMAIL>'
      )
      OR
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_user_meta_data->>'role' = 'admin'
      )
    )
  );

CREATE POLICY "Admin can update gallery images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'gallery-images' AND
    auth.role() = 'authenticated' AND
    (
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.email = '<EMAIL>'
      )
      OR
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_user_meta_data->>'role' = 'admin'
      )
    )
  );

CREATE POLICY "Admin can delete gallery images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'gallery-images' AND
    auth.role() = 'authenticated' AND
    (
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.email = '<EMAIL>'
      )
      OR
      EXISTS (
        SELECT 1 FROM auth.users 
        WHERE auth.users.id = auth.uid() 
        AND auth.users.raw_user_meta_data->>'role' = 'admin'
      )
    )
  );

-- Verify the setup
SELECT 
  id,
  email,
  raw_user_meta_data->>'role' as role
FROM auth.users 
WHERE email = '<EMAIL>';
