# Admin Features Documentation

## Overview

The Palm Beach Lakes Alumni Association website now includes comprehensive admin features for managing gallery photos and events with their respective flyers. This system allows administrators to easily add, edit, and organize content through a user-friendly interface.

## Features Added

### 🖼️ Gallery Management
- **Add Photos**: Upload high-quality images with metadata
- **Categorization**: Organize photos by category (reunions, sports, campus, events, achievements, historical)
- **Featured Items**: Mark special photos as featured
- **Metadata Support**: Add titles, descriptions, years, and photographer credits
- **Sort Order**: Control the display order of photos
- **Image Optimization**: Automatic image handling with fallbacks

### 📅 Event Management
- **Create Events**: Add new events with full details
- **Event Images**: Upload promotional images for events
- **Event Flyers**: Upload PDF or image flyers
- **Categories**: Organize events by type (reunion, social, fundraising, networking, educational, sports, community)
- **Featured Events**: Highlight important events
- **Date Management**: Full date and time support
- **Location Tracking**: Store event locations

### 🔐 Security Features
- **Role-Based Access**: Only admin users can access management features
- **Secure File Upload**: Protected file storage with validation
- **Row Level Security**: Database-level security policies
- **File Type Validation**: Ensures only appropriate file types are uploaded

## How to Use

### Accessing Admin Features

1. **Click Admin Button**: Look for the "Admin" button with shield icon in the navigation bar
2. **Enter Admin Password**: When prompted, enter the admin password: `Rams1989!`
3. **Access Dashboard**: Once authenticated, you'll be redirected to the admin dashboard
4. **Choose Management Section**: Select either "Events Management" or "Gallery Management"

### Admin Session Management

- **Session Duration**: Admin sessions last for 2 hours
- **Auto-Logout**: You'll be automatically logged out after 2 hours of inactivity
- **Manual Logout**: Click the logout icon next to "Admin Dashboard" to logout manually
- **Session Persistence**: Your admin session will persist across browser refreshes

### Gallery Management

#### Adding a New Photo
1. Go to **Admin Dashboard > Gallery Management**
2. Click **"Add New Item"**
3. Fill out the form:
   - **Image**: Upload JPG, PNG, or WebP (max 5MB)
   - **Title**: Descriptive title for the photo
   - **Category**: Select appropriate category
   - **Year**: When the photo was taken
   - **Description**: Optional description
   - **Photographer**: Optional photographer credit
   - **Featured**: Check if this should be highlighted
   - **Sort Order**: Number to control display order (lower = first)
4. Click **"Create Item"**

#### Editing Photos
1. Find the photo in the gallery grid
2. Click the **edit icon** (pencil)
3. Modify any fields as needed
4. Click **"Update Item"**

#### Organizing Photos
- Use **category filters** to view specific types of photos
- Adjust **sort order** numbers to change display sequence
- Mark important photos as **featured** for special highlighting

### Event Management

#### Creating a New Event
1. Go to **Admin Dashboard > Events Management**
2. Click **"Create Event"**
3. Fill out the form:
   - **Event Title**: Name of the event
   - **Category**: Type of event
   - **Date & Time**: When the event occurs
   - **Location**: Where the event takes place
   - **Description**: Detailed event description
   - **Event Image**: Optional promotional image (JPG, PNG, WebP, max 5MB)
   - **Event Flyer**: Optional flyer (Images or PDF, max 10MB)
   - **Featured**: Check for homepage highlighting
4. Click **"Create Event"**

#### Managing Events
- View all events sorted by date (upcoming first)
- Filter by category using the filter buttons
- Edit events by clicking the edit icon
- Delete events with the trash icon (with confirmation)
- See event status (upcoming vs. past)

## File Upload Guidelines

### Supported Formats
- **Images**: JPG, PNG, WebP
- **Flyers**: JPG, PNG, WebP, PDF

### Size Limits
- **Gallery Images**: 5MB maximum
- **Event Images**: 5MB maximum  
- **Event Flyers**: 10MB maximum

### Best Practices
- Use high-quality images for better display
- Compress images when possible to improve loading times
- Use descriptive filenames
- Include photographer credits when available
- Write clear, engaging descriptions

## Database Setup

If you're setting up a new instance, run the SQL commands in `database-setup.sql` in your Supabase SQL editor. This will:

1. Create the necessary database tables
2. Set up storage buckets for file uploads
3. Configure security policies
4. Add performance indexes
5. Insert sample data

## Technical Details

### Admin Authentication
- **Password-Based Access**: Simple password authentication system
- **Session Management**: 2-hour session duration with auto-logout
- **Local Storage**: Session state stored in browser localStorage
- **No Database Dependency**: Admin access doesn't require user accounts

### Database Tables
- **events**: Stores event information with image/flyer URLs
- **gallery_items**: Stores gallery photos with metadata

### Storage Buckets
- **event-images**: Stores event photos and flyers
- **gallery-images**: Stores gallery photos

### Security
- Row Level Security (RLS) enabled on all tables
- Admin-only write access
- Public read access for published content
- Secure file upload with validation
- Password protection for admin access

## Troubleshooting

### Common Issues

**Images not displaying:**
- Check file format (must be JPG, PNG, or WebP)
- Verify file size is under limits
- Ensure stable internet connection during upload

**Access denied:**
- Verify user has admin role in database
- Check authentication status
- Contact system administrator

**Upload failures:**
- Check file size limits
- Verify file format
- Try refreshing the page and uploading again

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Verify your admin permissions
3. Try refreshing the page
4. Contact the technical administrator

## Future Enhancements

Potential future improvements:
- Bulk photo upload
- Image editing tools
- Advanced search and filtering
- Photo albums/collections
- Event RSVP management
- Email notifications for new events
- Social media integration

---

*This documentation covers the admin features for managing gallery photos and events. For general website usage, refer to the main README.md file.*
