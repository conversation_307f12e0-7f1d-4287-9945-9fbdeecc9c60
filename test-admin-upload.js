// Test script for admin upload functionality
// Run this in browser console on the admin dashboard page

async function testAdminUpload() {
  console.log('🧪 Testing Admin Upload Functionality...');
  
  try {
    // Test 1: Check if admin authentication works
    console.log('1️⃣ Testing admin authentication...');
    
    const { supabase } = window;
    if (!supabase) {
      console.error('❌ Supabase client not found');
      return;
    }
    
    // Try to authenticate as admin
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Rams1989!'
    });
    
    if (authError) {
      console.error('❌ Admin authentication failed:', authError.message);
      return;
    }
    
    console.log('✅ Admin authentication successful');
    console.log('User ID:', authData.user.id);
    console.log('Email:', authData.user.email);
    
    // Test 2: Check user metadata
    console.log('2️⃣ Checking user metadata...');
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('❌ Failed to get user data:', userError.message);
      return;
    }
    
    console.log('User metadata:', userData.user.user_metadata);
    console.log('Raw metadata:', userData.user.raw_user_meta_data);
    
    // Test 3: Check storage buckets
    console.log('3️⃣ Testing storage bucket access...');
    
    const { data: eventBucket, error: eventBucketError } = await supabase.storage
      .from('event-images')
      .list('', { limit: 1 });
    
    if (eventBucketError) {
      console.error('❌ Event images bucket access failed:', eventBucketError.message);
    } else {
      console.log('✅ Event images bucket accessible');
    }
    
    const { data: galleryBucket, error: galleryBucketError } = await supabase.storage
      .from('gallery-images')
      .list('', { limit: 1 });
    
    if (galleryBucketError) {
      console.error('❌ Gallery images bucket access failed:', galleryBucketError.message);
    } else {
      console.log('✅ Gallery images bucket accessible');
    }
    
    // Test 4: Create a test file and try to upload
    console.log('4️⃣ Testing file upload...');
    
    // Create a small test image (1x1 pixel PNG)
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 1, 1);
    
    canvas.toBlob(async (blob) => {
      if (!blob) {
        console.error('❌ Failed to create test image');
        return;
      }
      
      const testFile = new File([blob], 'test-image.png', { type: 'image/png' });
      const testPath = `test/test-${Date.now()}.png`;
      
      console.log('📁 Uploading test file:', testPath);
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('event-images')
        .upload(testPath, testFile, {
          cacheControl: '3600',
          upsert: true
        });
      
      if (uploadError) {
        console.error('❌ Test upload failed:', uploadError.message);
        console.error('Error details:', uploadError);
      } else {
        console.log('✅ Test upload successful!');
        console.log('Upload data:', uploadData);
        
        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('event-images')
          .getPublicUrl(testPath);
        
        console.log('📎 Public URL:', publicUrl);
        
        // Clean up test file
        const { error: deleteError } = await supabase.storage
          .from('event-images')
          .remove([testPath]);
        
        if (deleteError) {
          console.warn('⚠️ Failed to clean up test file:', deleteError.message);
        } else {
          console.log('🧹 Test file cleaned up');
        }
      }
    }, 'image/png');
    
    console.log('🎉 Admin upload test completed!');
    
  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }
}

// Instructions
console.log(`
🔧 ADMIN UPLOAD TEST SCRIPT
==========================

To run this test:
1. Open browser developer tools (F12)
2. Navigate to the admin dashboard
3. Paste this entire script in the console
4. Run: testAdminUpload()

This will test:
✓ Admin authentication
✓ User metadata
✓ Storage bucket access  
✓ File upload functionality

Run the test now with: testAdminUpload()
`);

// Auto-run if this script is executed directly
if (typeof window !== 'undefined' && window.location.pathname.includes('/admin')) {
  console.log('🚀 Auto-running admin upload test...');
  setTimeout(testAdminUpload, 1000);
}
