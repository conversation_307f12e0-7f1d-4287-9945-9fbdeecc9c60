import PocketBase from 'pocketbase';
import type { RecordModel } from 'pocketbase';

// Initialize PocketBase client
export const pb = new PocketBase(import.meta.env.VITE_POCKETBASE_URL || 'http://127.0.0.1:8090');

// Auth helper functions
export const isUserValid = () => pb.authStore.isValid;
export const getCurrentUser = () => pb.authStore.model;

// Type for the auth store model
export type AuthModel = RecordModel & {
    email: string;
    name: string;
    avatar?: string;
    role: 'admin' | 'member';
    created: string;
    updated: string;
} 