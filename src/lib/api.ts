import { supabase } from './supabase';

// Types
export interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  location: string;
  image_url?: string;
  flyer_url?: string;
  category?: string;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface GalleryItem {
  id: string;
  title: string;
  description?: string;
  category: string;
  year?: string;
  image_url: string;
  photographer?: string;
  is_featured: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface Alumni {
  id: string;
  name: string;
  email: string;
  graduation_year: number;
  profession?: string;
  bio?: string;
  avatar?: string;
  linkedin?: string;
  location?: string;
  // Contact preferences
  preferred_contact?: string[];
  // Interest areas
  interests?: string[];
  // Event preferences
  event_types?: string[];
  // Leadership interest
  leadership_interest?: string;
  // Skills and resources
  skills_and_resources?: string;
  // What they want to see
  wishes_to_see?: string;
  // Additional comments
  additional_comments?: string;
  created_at: string;
  updated_at: string;
}

// Events API
export const eventsApi = {
  async getAll(): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .order('date', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Event | null> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async create(eventData: Partial<Event>): Promise<Event> {
    const { data, error } = await supabase
      .from('events')
      .insert([eventData])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, eventData: Partial<Event>): Promise<Event> {
    const { data, error } = await supabase
      .from('events')
      .update(eventData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async uploadEventImage(file: File, eventId: string): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${eventId}_image.${fileExt}`;
    const filePath = `events/${fileName}`;

    return uploadFile(file, 'event-images', filePath);
  },

  async uploadEventFlyer(file: File, eventId: string): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${eventId}_flyer.${fileExt}`;
    const filePath = `events/${fileName}`;

    return uploadFile(file, 'event-images', filePath);
  }
};

// Alumni API
export const alumniApi = {
  async getAll(): Promise<Alumni[]> {
    const { data, error } = await supabase
      .from('members')
      .select('*')
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Alumni | null> {
    const { data, error } = await supabase
      .from('members')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async create(alumniData: Partial<Alumni>): Promise<Alumni> {
    const { data, error } = await supabase
      .from('members')
      .insert([alumniData])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, alumniData: Partial<Alumni>): Promise<Alumni> {
    const { data, error } = await supabase
      .from('members')
      .update(alumniData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('members')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
};

// Admin Authentication for File Uploads
export const authenticateAdmin = async (): Promise<boolean> => {
  try {
    // Check if admin is already authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (user && user.email === '<EMAIL>') {
      return true;
    }

    // Sign in as admin user
    const { error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Rams1989!'
    });

    if (error) {
      console.error('Admin authentication failed:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Admin authentication error:', error);
    return false;
  }
};

// File Upload Utilities
export const uploadFile = async (
  file: File,
  bucket: string,
  path: string
): Promise<string> => {
  try {
    // Ensure admin is authenticated for uploads
    const isAuthenticated = await authenticateAdmin();
    if (!isAuthenticated) {
      console.warn('Admin authentication failed, using fallback');
      return createLocalFileUrl(file);
    }

    // Try Supabase storage first
    const { error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.warn('Supabase storage error, using fallback:', error.message);
      // Fallback to local file URL for development
      return createLocalFileUrl(file);
    }

    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return publicUrl;
  } catch (error) {
    console.warn('Storage upload failed, using fallback:', error);
    // Fallback to local file URL for development
    return createLocalFileUrl(file);
  }
};

// Fallback function for development - creates a local blob URL
const createLocalFileUrl = (file: File): string => {
  const url = URL.createObjectURL(file);
  console.log('Created local file URL:', url);
  return url;
};

export const deleteFile = async (bucket: string, path: string): Promise<void> => {
  const { error } = await supabase.storage
    .from(bucket)
    .remove([path]);

  if (error) throw error;
};

// Gallery API
export const galleryApi = {
  async getAll(): Promise<GalleryItem[]> {
    const { data, error } = await supabase
      .from('gallery_items')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  async getByCategory(category: string): Promise<GalleryItem[]> {
    const { data, error } = await supabase
      .from('gallery_items')
      .select('*')
      .eq('category', category)
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  async getFeatured(): Promise<GalleryItem[]> {
    const { data, error } = await supabase
      .from('gallery_items')
      .select('*')
      .eq('is_featured', true)
      .order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<GalleryItem | null> {
    const { data, error } = await supabase
      .from('gallery_items')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async create(itemData: Partial<GalleryItem>): Promise<GalleryItem> {
    const { data, error } = await supabase
      .from('gallery_items')
      .insert([itemData])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async update(id: string, itemData: Partial<GalleryItem>): Promise<GalleryItem> {
    const { data, error } = await supabase
      .from('gallery_items')
      .update(itemData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('gallery_items')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async uploadImage(file: File, itemId: string): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${itemId}.${fileExt}`;
    const filePath = `gallery/${fileName}`;

    return uploadFile(file, 'gallery-images', filePath);
  }
};
