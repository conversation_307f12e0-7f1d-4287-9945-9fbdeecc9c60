import { useState, useRef } from 'react';
import type { ChangeEvent, FormEvent } from 'react';
import { FiUpload, FiSave, FiX } from 'react-icons/fi';
import { profileImageApi } from '../../lib/api';
import { useAuth } from '../../hooks/useAuth';
import '../layout/backgroundStyles.css';
import type { Member } from '../MemberCard';

interface ProfileEditorProps {
  initialData?: Partial<Member>;
  onSave: (profileData: Partial<Member>, imageFile?: File) => Promise<void>;
  onCancel: () => void;
}

const ProfileEditor = ({ initialData = {}, onSave, onCancel }: ProfileEditorProps) => {
  const { user } = useAuth();
  const [profileData, setProfileData] = useState({
    name: initialData.name || '',
    graduation_year: initialData.graduation_year?.toString() || '',
    profession: initialData.profession || '',
    email: initialData.email || '',
    linkedin: initialData.linkedin || '',
    location: initialData.location || '',
    bio: initialData.bio || '',
  });
  
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(initialData.avatar_url || null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  
  const handleImageChange = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      try {
        // Create a preview
        const reader = new FileReader();
        reader.onloadend = () => {
          setImagePreview(reader.result as string);
        };
        reader.readAsDataURL(file);
        
        setImageFile(file);
        
        // Clear error if exists
        if (errors.image) {
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.image;
            return newErrors;
          });
        }
      } catch (error) {
        setErrors(prev => ({ 
          ...prev, 
          image: error instanceof Error ? error.message : 'Failed to process image' 
        }));
      }
    }
  };
  
  const triggerImageUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  const removeImage = async () => {
    if (user && initialData.avatar_url) {
      try {
        await profileImageApi.delete(user.id);
      } catch (error) {
        console.error('Failed to delete existing image:', error);
      }
    }
    
    setImageFile(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!profileData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!profileData.graduation_year.trim()) {
      newErrors.graduation_year = 'Graduation year is required';
    } else if (!/^\d{4}$/.test(profileData.graduation_year)) {
      newErrors.graduation_year = 'Please enter a valid 4-digit year';
    }
    
    if (!profileData.profession.trim()) {
      newErrors.profession = 'Profession is required';
    }
    
    if (profileData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (profileData.linkedin && !profileData.linkedin.includes('linkedin.com')) {
      newErrors.linkedin = 'Please enter a valid LinkedIn URL';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsUploading(true);
    
    try {
      // Handle image upload first if there's a new image
      let avatarUrl = initialData.avatar_url;
      if (imageFile && user) {
        avatarUrl = await profileImageApi.upload(imageFile, user.id);
      }
      
      // Prepare profile data
      const updatedProfile = {
        ...profileData,
        graduation_year: parseInt(profileData.graduation_year),
        avatar_url: avatarUrl
      };
      
      // Save the profile
      await onSave(updatedProfile, imageFile || undefined);
    } catch (error) {
      setErrors(prev => ({ 
        ...prev, 
        submit: error instanceof Error ? error.message : 'Failed to save profile' 
      }));
    } finally {
      setIsUploading(false);
    }
  };
  
  return (
    <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200 profile-editor-container">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-display font-bold text-gray-900">
          {initialData.name ? 'Edit Your Profile' : 'Create Your Profile'}
        </h2>
        <button 
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
          aria-label="Cancel"
        >
          <FiX size={24} />
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Image */}
        <div className="flex flex-col items-center mb-8">
          <div 
            className="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center relative profile-image-container"
          >
            {imagePreview ? (
              <>
                <img 
                  src={imagePreview} 
                  alt="Profile preview" 
                  className="w-full h-full object-cover"
                />
                <button 
                  type="button"
                  onClick={removeImage}
                  className="absolute bottom-0 right-0 bg-crimson-700 text-white p-1 rounded-full"
                  aria-label="Remove image"
                >
                  <FiX size={16} />
                </button>
              </>
            ) : (
              <div 
                className="text-center text-gray-400 cursor-pointer"
                onClick={triggerImageUpload}
              >
                <FiUpload size={24} className="mx-auto mb-2" />
                <span className="text-sm">Add Photo</span>
              </div>
            )}
          </div>
          
          <input 
            type="file" 
            ref={fileInputRef} 
            onChange={handleImageChange} 
            className="hidden" 
            accept="image/png, image/jpeg, image/jpg"
          />
          
          <button
            type="button"
            onClick={triggerImageUpload}
            className="btn btn-outline border-crimson-700 text-crimson-700 text-sm hover:bg-crimson-50"
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : 'Upload Professional Headshot'}
          </button>
          
          {errors.image && (
            <p className="text-red-500 text-sm mt-1">{errors.image}</p>
          )}
          <p className="text-gray-500 text-xs mt-2">
            Upload a professional headshot (JPG or PNG, max 2MB)
          </p>
        </div>
        
        {/* Form Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="font-bold text-gray-900 mb-4">Basic Information</h3>
            
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Full Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={profileData.name}
                onChange={handleChange}
                className={`mt-1 block w-full rounded-md shadow-sm ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label htmlFor="graduation_year" className="block text-sm font-medium text-gray-700">
                Graduation Year *
              </label>
              <input
                type="text"
                id="graduation_year"
                name="graduation_year"
                value={profileData.graduation_year}
                onChange={handleChange}
                className={`mt-1 block w-full rounded-md shadow-sm ${
                  errors.graduation_year ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {errors.graduation_year && (
                <p className="mt-1 text-sm text-red-600">{errors.graduation_year}</p>
              )}
            </div>

            <div>
              <label htmlFor="profession" className="block text-sm font-medium text-gray-700">
                Profession *
              </label>
              <input
                type="text"
                id="profession"
                name="profession"
                value={profileData.profession}
                onChange={handleChange}
                className={`mt-1 block w-full rounded-md shadow-sm ${
                  errors.profession ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {errors.profession && (
                <p className="mt-1 text-sm text-red-600">{errors.profession}</p>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="font-bold text-gray-900 mb-4">Contact Information</h3>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={profileData.email}
                onChange={handleChange}
                className={`mt-1 block w-full rounded-md shadow-sm ${
                  errors.email ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            <div>
              <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700">
                LinkedIn Profile
              </label>
              <input
                type="url"
                id="linkedin"
                name="linkedin"
                value={profileData.linkedin}
                onChange={handleChange}
                className={`mt-1 block w-full rounded-md shadow-sm ${
                  errors.linkedin ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="https://linkedin.com/in/your-profile"
              />
              {errors.linkedin && (
                <p className="mt-1 text-sm text-red-600">{errors.linkedin}</p>
              )}
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                Location
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={profileData.location}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
                placeholder="City, State"
              />
            </div>
          </div>
        </div>

        {/* Bio */}
        <div>
          <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
            Bio
          </label>
          <textarea
            id="bio"
            name="bio"
            value={profileData.bio}
            onChange={handleChange}
            rows={4}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
            placeholder="Tell us about yourself..."
          />
        </div>

        {/* Error Message */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {errors.submit}
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-crimson-600 text-white rounded-md hover:bg-crimson-700"
            disabled={isUploading}
          >
            {isUploading ? 'Saving...' : 'Save Profile'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProfileEditor; 