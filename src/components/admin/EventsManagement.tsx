import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FiPlus, FiEdit, FiTrash2, FiUpload, FiCalendar, FiSave, FiX, FiImage, FiFileText } from 'react-icons/fi';
import { eventsApi, type Event } from '../../lib/api';

interface EventFormData {
  title: string;
  description: string;
  date: string;
  location: string;
  category: string;
  is_featured: boolean;
}

const EVENT_CATEGORIES = [
  'reunion',
  'social',
  'fundraising',
  'networking',
  'educational',
  'sports',
  'community'
];

const EventForm = ({ 
  event, 
  onSubmit, 
  onCancel 
}: { 
  event?: Event; 
  onSubmit: (data: EventFormData, imageFile?: File, flyerFile?: File) => void;
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState<EventFormData>({
    title: event?.title || '',
    description: event?.description || '',
    date: event?.date ? new Date(event.date).toISOString().slice(0, 16) : '',
    location: event?.location || '',
    category: event?.category || 'social',
    is_featured: event?.is_featured || false
  });
  
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [flyerFile, setFlyerFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(event?.image_url || null);
  const [flyerPreview, setFlyerPreview] = useState<string | null>(event?.flyer_url || null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, image: 'Please upload a JPG, PNG, or WebP image' }));
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, image: 'Image must be smaller than 5MB' }));
      return;
    }

    setImageFile(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Clear error
    if (errors.image) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.image;
        return newErrors;
      });
    }
  };

  const handleFlyerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type (images and PDFs)
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf'];
    if (!validTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, flyer: 'Please upload a JPG, PNG, WebP, or PDF file' }));
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, flyer: 'Flyer must be smaller than 10MB' }));
      return;
    }

    setFlyerFile(file);
    
    // Create preview for images only
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFlyerPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setFlyerPreview(null);
    }

    // Clear error
    if (errors.flyer) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.flyer;
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.date) {
      newErrors.date = 'Date is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData, imageFile || undefined, flyerFile || undefined);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-lg p-6"
    >
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold">
          {event ? 'Edit Event' : 'Create New Event'}
        </h3>
        <button
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
        >
          <FiX size={24} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              Event Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter event title"
            />
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">{errors.title}</p>
            )}
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              id="category"
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {EVENT_CATEGORIES.map(cat => (
                <option key={cat} value={cat}>
                  {cat.charAt(0).toUpperCase() + cat.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Date */}
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
              Date & Time <span className="text-red-500">*</span>
            </label>
            <input
              type="datetime-local"
              id="date"
              value={formData.date}
              onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.date && (
              <p className="text-red-500 text-sm mt-1">{errors.date}</p>
            )}
          </div>

          {/* Location */}
          <div>
            <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
              Location <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="location"
              value={formData.location}
              onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Event location"
            />
            {errors.location && (
              <p className="text-red-500 text-sm mt-1">{errors.location}</p>
            )}
          </div>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description <span className="text-red-500">*</span>
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe the event..."
          />
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description}</p>
          )}
        </div>

        {/* File Uploads */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Event Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Event Image
            </label>
            
            <div className="space-y-3">
              {imagePreview && (
                <img
                  src={imagePreview}
                  alt="Event preview"
                  className="w-full h-32 object-cover rounded-lg border"
                />
              )}
              
              <label className="cursor-pointer bg-white px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 flex items-center justify-center">
                <FiImage className="mr-2" />
                {imageFile ? 'Change Image' : 'Upload Image'}
                <input
                  type="file"
                  className="sr-only"
                  accept="image/*"
                  onChange={handleImageChange}
                />
              </label>
              
              {errors.image && (
                <p className="text-red-500 text-sm">{errors.image}</p>
              )}
              <p className="text-xs text-gray-500">
                JPG, PNG, or WebP up to 5MB
              </p>
            </div>
          </div>

          {/* Event Flyer */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Event Flyer
            </label>
            
            <div className="space-y-3">
              {flyerPreview && (
                <img
                  src={flyerPreview}
                  alt="Flyer preview"
                  className="w-full h-32 object-cover rounded-lg border"
                />
              )}
              
              {flyerFile && !flyerPreview && (
                <div className="flex items-center p-3 bg-gray-50 rounded-lg border">
                  <FiFileText className="mr-2 text-gray-500" />
                  <span className="text-sm text-gray-700">{flyerFile.name}</span>
                </div>
              )}
              
              <label className="cursor-pointer bg-white px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 flex items-center justify-center">
                <FiUpload className="mr-2" />
                {flyerFile ? 'Change Flyer' : 'Upload Flyer'}
                <input
                  type="file"
                  className="sr-only"
                  accept="image/*,application/pdf"
                  onChange={handleFlyerChange}
                />
              </label>
              
              {errors.flyer && (
                <p className="text-red-500 text-sm">{errors.flyer}</p>
              )}
              <p className="text-xs text-gray-500">
                Images or PDF up to 10MB
              </p>
            </div>
          </div>
        </div>

        {/* Featured */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_featured"
            checked={formData.is_featured}
            onChange={(e) => setFormData(prev => ({ ...prev, is_featured: e.target.checked }))}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700">
            Featured event (will appear prominently on the homepage)
          </label>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
          >
            <FiSave className="mr-2" />
            {event ? 'Update' : 'Create'} Event
          </button>
        </div>
      </form>
    </motion.div>
  );
};

const EventsManagement = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    loadEvents();
  }, []);

  const loadEvents = async () => {
    try {
      const eventsList = await eventsApi.getAll();
      setEvents(eventsList);
    } catch (error) {
      console.error('Error loading events:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEvent = async (formData: EventFormData, imageFile?: File, flyerFile?: File) => {
    try {
      setLoading(true);
      setErrorMessage('');

      // Create the event first to get an ID
      const newEvent = await eventsApi.create({
        ...formData,
        date: new Date(formData.date).toISOString()
      });

      // Upload files if provided
      let updateData: Partial<Event> = {};

      if (imageFile) {
        const imageUrl = await eventsApi.uploadEventImage(imageFile, newEvent.id);
        updateData.image_url = imageUrl;
      }

      if (flyerFile) {
        const flyerUrl = await eventsApi.uploadEventFlyer(flyerFile, newEvent.id);
        updateData.flyer_url = flyerUrl;
      }

      // Update event with file URLs if any were uploaded
      if (Object.keys(updateData).length > 0) {
        const updatedEvent = await eventsApi.update(newEvent.id, updateData);
        setEvents(prev => [...prev, updatedEvent]);
      } else {
        setEvents(prev => [...prev, newEvent]);
      }

      setShowForm(false);
      setSuccessMessage('Event created successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Error creating event:', error);
      setErrorMessage('Failed to create event. Please try again.');
      setTimeout(() => setErrorMessage(''), 5000);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateEvent = async (formData: EventFormData, imageFile?: File, flyerFile?: File) => {
    if (!editingEvent) return;

    try {
      setLoading(true);

      let updateData: Partial<Event> = {
        ...formData,
        date: new Date(formData.date).toISOString()
      };

      // Upload new files if provided
      if (imageFile) {
        const imageUrl = await eventsApi.uploadEventImage(imageFile, editingEvent.id);
        updateData.image_url = imageUrl;
      }

      if (flyerFile) {
        const flyerUrl = await eventsApi.uploadEventFlyer(flyerFile, editingEvent.id);
        updateData.flyer_url = flyerUrl;
      }

      const updatedEvent = await eventsApi.update(editingEvent.id, updateData);

      setEvents(prev =>
        prev.map(event => event.id === editingEvent.id ? updatedEvent : event)
      );

      setEditingEvent(null);
      setShowForm(false);
    } catch (error) {
      console.error('Error updating event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (id: string) => {
    if (!confirm('Are you sure you want to delete this event?')) return;

    try {
      await eventsApi.delete(id);
      setEvents(prev => prev.filter(event => event.id !== id));
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };

  const filteredEvents = selectedCategory === 'all'
    ? events
    : events.filter(event => event.category === selectedCategory);

  // Sort events by date (upcoming first)
  const sortedEvents = [...filteredEvents].sort((a, b) =>
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  if (loading && events.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (showForm) {
    return (
      <EventForm
        event={editingEvent || undefined}
        onSubmit={editingEvent ? handleUpdateEvent : handleCreateEvent}
        onCancel={() => {
          setShowForm(false);
          setEditingEvent(null);
        }}
      />
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Events Management</h2>
          <p className="mt-1 text-gray-600">Create and manage alumni events with images and flyers</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm"
        >
          <FiPlus className="mr-2" />
          Create Event
        </button>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md mb-4">
          {successMessage}
        </div>
      )}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md mb-4">
          {errorMessage}
        </div>
      )}

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => setSelectedCategory('all')}
          className={`px-3 py-1 rounded-full text-sm ${
            selectedCategory === 'all'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          All ({events.length})
        </button>
        {EVENT_CATEGORIES.map(category => {
          const count = events.filter(event => event.category === category).length;
          return (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 rounded-full text-sm ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {category.charAt(0).toUpperCase() + category.slice(1)} ({count})
            </button>
          );
        })}
      </div>

      {/* Events List */}
      <div className="space-y-4">
        {sortedEvents.map((event) => {
          const eventDate = new Date(event.date);
          const isUpcoming = eventDate > new Date();

          return (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-md p-6"
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{event.title}</h3>
                    {event.is_featured && (
                      <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                        Featured
                      </span>
                    )}
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      isUpcoming
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {isUpcoming ? 'Upcoming' : 'Past'}
                    </span>
                  </div>

                  <div className="flex items-center text-sm text-gray-600 mb-2">
                    <FiCalendar className="mr-1" />
                    {eventDate.toLocaleDateString()} at {eventDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    <span className="mx-2">•</span>
                    {event.location}
                    {event.category && (
                      <>
                        <span className="mx-2">•</span>
                        {event.category.charAt(0).toUpperCase() + event.category.slice(1)}
                      </>
                    )}
                  </div>

                  <p className="text-gray-700 mb-3 line-clamp-2">{event.description}</p>

                  <div className="flex items-center space-x-4">
                    {event.image_url && (
                      <span className="flex items-center text-sm text-gray-500">
                        <FiImage className="mr-1" />
                        Image
                      </span>
                    )}
                    {event.flyer_url && (
                      <span className="flex items-center text-sm text-gray-500">
                        <FiFileText className="mr-1" />
                        Flyer
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => {
                      setEditingEvent(event);
                      setShowForm(true);
                    }}
                    className="text-blue-600 hover:text-blue-800 p-2"
                  >
                    <FiEdit size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteEvent(event.id)}
                    className="text-red-600 hover:text-red-800 p-2"
                  >
                    <FiTrash2 size={16} />
                  </button>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {sortedEvents.length === 0 && (
        <div className="text-center py-12">
          <FiCalendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No events</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first event.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center mx-auto"
            >
              <FiPlus className="mr-2" />
              Create Event
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export { EventForm };
export default EventsManagement;
