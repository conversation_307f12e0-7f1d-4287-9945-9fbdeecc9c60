import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import { motion } from 'framer-motion';
import { FiShield, FiLogOut } from 'react-icons/fi';
import AdminLogin from '../admin/AdminLogin';

const Navbar = () => {
  const { user, signOut } = useAuth();
  const { isAdminAuthenticated, authenticateAdmin, logoutAdmin } = useAdminAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [showAdminLogin, setShowAdminLogin] = useState(false);

  const handleAdminAccess = () => {
    authenticateAdmin();
    setShowAdminLogin(false);
    navigate('/admin');
  };

  const handleAdminLogout = () => {
    logoutAdmin();
    navigate('/');
  };

  return (
    <>
      {showAdminLogin && (
        <AdminLogin
          onAdminAccess={handleAdminAccess}
          onCancel={() => setShowAdminLogin(false)}
        />
      )}
    <nav className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-20">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <motion.img
                whileHover={{ scale: 1.05 }}
                className="h-16 w-auto"
                src="/logo.png"
                alt="Palm Beach Lakes Alumni"
              />
            </Link>
            <div className="hidden sm:ml-8 sm:flex sm:space-x-8">
              <NavLink to="/" isActive={location.pathname === "/"}>Home</NavLink>
              <NavLink to="/events" isActive={location.pathname === "/events"}>Events</NavLink>
              <NavLink to="/gallery" isActive={location.pathname === "/gallery"}>Gallery</NavLink>
              <NavLink to="/about" isActive={location.pathname === "/about"}>About</NavLink>
              <NavLink to="/contact" isActive={location.pathname === "/contact"}>Contact</NavLink>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Admin Access Button */}
            {isAdminAuthenticated ? (
              <div className="flex items-center space-x-3">
                <Link
                  to="/admin"
                  className="flex items-center text-[#7D0E17] hover:text-[#590A11] font-medium hover:font-semibold transition-all"
                >
                  <FiShield className="mr-1" size={16} />
                  Admin Dashboard
                </Link>
                <button
                  onClick={handleAdminLogout}
                  className="flex items-center text-gray-600 hover:text-gray-800 font-medium transition-all"
                  title="Logout Admin"
                >
                  <FiLogOut size={16} />
                </button>
              </div>
            ) : (
              <button
                onClick={() => setShowAdminLogin(true)}
                className="flex items-center text-[#7D0E17] hover:text-[#590A11] font-medium hover:font-semibold transition-all"
                title="Admin Access"
              >
                <FiShield className="mr-1" size={16} />
                Admin
              </button>
            )}

            {/* User Authentication */}
            {user ? (
              <div className="flex items-center space-x-4">
                <button
                  onClick={signOut}
                  className="bg-[#7D0E17] text-white px-6 py-2.5 rounded-md hover:brightness-110 transition-all font-medium"
                >
                  Logout
                </button>
              </div>
            ) : (
              <Link
                to="/join"
                className="bg-[#7D0E17] text-white px-6 py-2.5 rounded-md hover:brightness-110 transition-all font-medium"
              >
                Join Now
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
    </>
  );
};

// NavLink component with active state styling
const NavLink = ({ to, children, isActive }: { to: string; children: React.ReactNode; isActive: boolean }) => (
  <Link
    to={to}
    className={`inline-flex items-center px-1 pt-1 text-base font-medium border-b-2 transition-all
                ${isActive 
                  ? 'text-[#7D0E17] border-[#7D0E17] font-semibold' 
                  : 'text-[#7D0E17] border-transparent hover:border-[#7D0E17] hover:font-semibold'}`}
  >
    {children}
  </Link>
);

export default Navbar; 