import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FiChevronDown, FiChevronRight, FiUsers, FiHeart, FiMapPin, FiSearch, FiShield, FiLogOut } from 'react-icons/fi';
import { FaFacebookF, FaLinkedinIn, FaInstagram, FaTwitter, FaYoutube } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useAdminAuth } from '../hooks/useAdminAuth';
import AdminLogin from './admin/AdminLogin';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [showAdminLogin, setShowAdminLogin] = useState(false);
  const { isAdminAuthenticated, authenticateAdmin, logoutAdmin } = useAdminAuth();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 20;
      setIsScrolled(scrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when navigating
  useEffect(() => {
    setIsOpen(false);
    setActiveDropdown(null);
  }, [location.pathname]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setActiveDropdown(null);
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleAdminAccess = () => {
    authenticateAdmin();
    setShowAdminLogin(false);
    navigate('/admin');
  };

  const handleAdminLogout = () => {
    logoutAdmin();
    navigate('/');
  };

  const handleScrollToSection = (e: React.MouseEvent, path: string) => {
    // Only handle scroll for hash links, let normal navigation work for regular paths
    if (path.includes('#')) {
      e.preventDefault();
      const [_, section] = path.split('#');
      const element = document.getElementById(section);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        setActiveDropdown(null);
      }
    } else {
      // For regular paths, just close the dropdown and let the Link handle navigation
      setActiveDropdown(null);
    }
  };

  const handleDropdownClick = (e: React.MouseEvent, dropdownName: string) => {
    e.stopPropagation();
    setActiveDropdown(activeDropdown === dropdownName ? null : dropdownName);
  };

  const navItems = [
    { name: 'ABOUT', path: '/about', type: 'link' },
    {
      name: 'COMMUNITY',
      type: 'dropdown',
      items: [
        { name: 'Alumni Directory', path: '/directory', icon: FiUsers, description: 'Connect with fellow alumni' },
        { name: 'Get Involved', path: '/get-involved', icon: FiHeart, description: 'Join our initiatives' },
        { name: 'Local Chapters', path: '/community', icon: FiMapPin, description: 'Find alumni near you' },
      ]
    },
    { name: 'EVENTS', path: '/events', type: 'link' },
    { name: 'GALLERY', path: '/gallery', type: 'link' },
    { name: 'DIRECTORY', path: '/directory', type: 'link' },
    { name: 'CONTACT', path: '/contact', type: 'link' }
  ];

  const socialLinks = [
    { icon: FaFacebookF, href: '#', label: 'Facebook' },
    { icon: FaLinkedinIn, href: '#', label: 'LinkedIn' },
    { icon: FaInstagram, href: '#', label: 'Instagram' },
    { icon: FaTwitter, href: '#', label: 'Twitter' },
    { icon: FaYoutube, href: '#', label: 'YouTube' },
  ];

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const dropdownVariants = {
    hidden: { 
      opacity: 0, 
      y: -10,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.2,
        ease: "easeOut"
      }
    },
    exit: { 
      opacity: 0, 
      y: -10,
      scale: 0.95,
      transition: {
        duration: 0.15
      }
    }
  };

  return (
    <>
      {/* Admin Login Modal */}
      {showAdminLogin && (
        <AdminLogin
          onAdminAccess={handleAdminAccess}
          onCancel={() => setShowAdminLogin(false)}
        />
      )}

      {/* Top Bar with Social Icons */}
      <div className={`fixed w-full z-[99] bg-white border-b border-gray-100 transition-all duration-300 ${
        isScrolled ? 'h-0 overflow-hidden opacity-0' : 'h-auto opacity-100'
      }`}>
        <div className="container-custom py-1.5">
          <div className="flex justify-end items-center space-x-4">
            {/* Social Icons */}
            <div className="flex items-center space-x-2">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  whileHover={{ scale: 1.1, y: -1 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-7 h-7 bg-gray-100 hover:bg-crimson-500 text-gray-600 hover:text-white rounded-full flex items-center justify-center transition-all duration-200"
                  aria-label={social.label}
                >
                  <social.icon className="w-3 h-3" />
                </motion.a>
              ))}
            </div>
            
            {/* Donate Button */}
            <motion.a
              href="#"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-3 py-1 bg-crimson-500 hover:bg-crimson-600 text-white text-xs font-semibold rounded-md transition-all duration-200"
            >
              DONATE
            </motion.a>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5 }}
        className={`fixed w-full z-[100] transition-all duration-500 ${
          isScrolled
            ? 'top-0 bg-white/98 backdrop-blur-md shadow-lg border-b border-gray-100'
            : 'top-10 bg-white/95 backdrop-blur-sm shadow-sm'
        }`}
      >
        <div className="container-custom relative z-10">
          <div className="flex items-center justify-between py-3">
            {/* Logo */}
            <Link 
              to="/" 
              className="relative z-[101]"
            >
              <motion.div 
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center"
              >
                <div className="w-10 h-10 flex items-center justify-center">
                  <img 
                    src="/images/logo/pblnaa-logo.png" 
                    alt="PBLNAA Logo" 
                    className="w-9 h-9 object-contain" 
                  />
                </div>
                <div className="ml-2.5 text-left">
                  <div className="font-display text-lg font-bold leading-tight text-gray-900 tracking-tight">
                    PALM BEACH LAKES
                  </div>
                  <div className="font-medium text-xs text-crimson-600 tracking-wide">
                    Alumni Association
                  </div>
                </div>
              </motion.div>
            </Link>

            {/* Mobile menu button */}
            <button
              type="button"
              className="lg:hidden rounded-md p-2 text-gray-700 focus:outline-none hover:bg-gray-100 transition-colors"
              onClick={() => setIsOpen(!isOpen)}
            >
              <span className="sr-only">Open main menu</span>
              <div className="w-5 h-5 flex flex-col justify-around">
                <span className={`w-full h-0.5 bg-gray-700 rounded-full transform transition duration-300 ${isOpen ? 'rotate-45 translate-y-1.5' : ''}`}></span>
                <span className={`w-full h-0.5 bg-gray-700 rounded-full transition duration-300 ${isOpen ? 'opacity-0' : 'opacity-100'}`}></span>
                <span className={`w-full h-0.5 bg-gray-700 rounded-full transform transition duration-300 ${isOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></span>
              </div>
            </button>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center">
              {/* Navigation Items */}
              <div className="flex items-center space-x-6">
                {navItems.map((item) => (
                  <div key={item.name} className="relative">
                    {item.type === 'dropdown' ? (
                      <div>
                        <button
                          onClick={(e) => handleDropdownClick(e, item.name)}
                          className={`flex items-center font-semibold text-sm tracking-wide transition-all duration-200 ${
                            activeDropdown === item.name
                              ? 'text-crimson-600'
                              : 'text-gray-700 hover:text-crimson-600'
                          }`}
                        >
                          {item.name}
                          <FiChevronDown 
                            className={`ml-1 w-4 h-4 transition-transform duration-200 ${
                              activeDropdown === item.name ? 'rotate-180' : ''
                            }`} 
                          />
                        </button>

                        <AnimatePresence>
                          {activeDropdown === item.name && (
                            <motion.div
                              variants={dropdownVariants}
                              initial="hidden"
                              animate="visible"
                              exit="exit"
                              className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-gray-100 overflow-hidden z-50"
                            >
                              <div className="p-2">
                                {item.items?.map((subItem, _) => (
                                  <Link
                                    key={subItem.name}
                                    to={subItem.path}
                                    onClick={(e) => handleScrollToSection(e, subItem.path)}
                                    className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
                                  >
                                    <div className="w-9 h-9 bg-crimson-50 rounded-lg flex items-center justify-center mr-3 group-hover:bg-crimson-100 transition-colors">
                                      <subItem.icon className="w-4 h-4 text-crimson-600" />
                                    </div>
                                    <div className="flex-1">
                                      <div className="font-semibold text-gray-900 group-hover:text-crimson-700 transition-colors text-sm">
                                        {subItem.name}
                                      </div>
                                      <div className="text-xs text-gray-500 mt-1">
                                        {subItem.description}
                                      </div>
                                    </div>
                                    <FiChevronRight className="w-4 h-4 text-gray-400 group-hover:text-crimson-600 group-hover:translate-x-1 transition-all duration-200" />
                                  </Link>
                                ))}
                              </div>
                              
                              {/* Dropdown footer */}
                              <div className="bg-gray-50 p-3 border-t border-gray-100">
                                <div className="text-xs text-gray-500 text-center">
                                  Need help? <Link to="/contact" className="text-crimson-600 hover:text-crimson-700 font-medium">Contact us</Link>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    ) : (
                      <Link
                        to={item.path!}
                        className={`font-semibold text-sm tracking-wide transition-all duration-200 ${
                          isActive(item.path!)
                            ? 'text-crimson-600'
                            : 'text-gray-700 hover:text-crimson-600'
                        }`}
                      >
                        {item.name}
                      </Link>
                    )}
                  </div>
                ))}
              </div>

              {/* Right Side Icons */}
              <div className="flex items-center ml-8 space-x-4">
                {/* Admin Access Button */}
                {isAdminAuthenticated ? (
                  <div className="flex items-center space-x-3">
                    <Link
                      to="/admin"
                      className="flex items-center text-crimson-600 hover:text-crimson-700 font-semibold transition-all"
                    >
                      <FiShield className="mr-1" size={16} />
                      Admin Dashboard
                    </Link>
                    <button
                      onClick={handleAdminLogout}
                      className="flex items-center text-gray-600 hover:text-gray-800 font-medium transition-all"
                      title="Logout Admin"
                    >
                      <FiLogOut size={16} />
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={() => setShowAdminLogin(true)}
                    className="flex items-center text-crimson-600 hover:text-crimson-700 font-semibold transition-all"
                    title="Admin Access"
                  >
                    <FiShield className="mr-1" size={16} />
                    Admin
                  </button>
                )}

                {isScrolled && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex items-center space-x-2"
                  >
                    {socialLinks.slice(0, 3).map((social, index) => (
                      <motion.a
                        key={index}
                        href={social.href}
                        whileHover={{ scale: 1.1, y: -1 }}
                        whileTap={{ scale: 0.95 }}
                        className="w-7 h-7 bg-gray-100 hover:bg-crimson-500 text-gray-600 hover:text-white rounded-full flex items-center justify-center transition-all duration-200"
                        aria-label={social.label}
                      >
                        <social.icon className="w-3 h-3" />
                      </motion.a>
                    ))}
                  </motion.div>
                )}

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-9 h-9 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-lg flex items-center justify-center transition-all duration-200"
                >
                  <FiSearch className="w-4 h-4" />
                </motion.button>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    to="/join"
                    className="px-4 py-2 bg-crimson-500 hover:bg-crimson-600 text-white rounded-lg text-sm font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    JOIN NOW
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isOpen && (
            <motion.div 
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="lg:hidden bg-white/98 backdrop-blur-md border-t border-gray-100 overflow-hidden"
            >
              <div className="px-4 py-3 space-y-1">
                {navItems.map((item) => (
                  <div key={item.name}>
                    {item.type === 'dropdown' ? (
                      <div>
                        <button
                          onClick={(e) => handleDropdownClick(e, `mobile-${item.name}`)}
                          className="w-full text-left px-3 py-2.5 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-between"
                        >
                          {item.name}
                          <FiChevronDown 
                            className={`w-4 h-4 transition-transform duration-200 ${
                              activeDropdown === `mobile-${item.name}` ? 'rotate-180' : ''
                            }`} 
                          />
                        </button>
                        
                        <AnimatePresence>
                          {activeDropdown === `mobile-${item.name}` && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.2 }}
                              className="ml-3 mt-1 space-y-1 overflow-hidden"
                            >
                              {item.items?.map((subItem) => (
                                <Link
                                  key={subItem.name}
                                  to={subItem.path}
                                  onClick={(e) => {
                                    handleScrollToSection(e, subItem.path);
                                    if (!subItem.path.includes('#')) {
                                      setIsOpen(false);
                                    }
                                  }}
                                  className="flex items-center px-3 py-2 text-gray-600 rounded-lg hover:bg-gray-50 hover:text-crimson-600 transition-colors"
                                >
                                  <subItem.icon className="w-4 h-4 mr-2" />
                                  {subItem.name}
                                </Link>
                              ))}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    ) : (
                      <Link
                        to={item.path!}
                        className={`block px-3 py-2.5 rounded-lg font-semibold transition-colors ${
                          isActive(item.path!)
                            ? 'bg-crimson-50 text-crimson-700'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-crimson-600'
                        }`}
                        onClick={() => setIsOpen(false)}
                      >
                        {item.name}
                      </Link>
                    )}
                  </div>
                ))}

                <div className="pt-3 border-t border-gray-100 space-y-2">
                  {/* Mobile Admin Button */}
                  {isAdminAuthenticated ? (
                    <div className="flex items-center justify-between">
                      <Link
                        to="/admin"
                        className="flex items-center text-crimson-600 hover:text-crimson-700 font-semibold transition-all px-3 py-2"
                        onClick={() => setIsOpen(false)}
                      >
                        <FiShield className="mr-2" size={16} />
                        Admin Dashboard
                      </Link>
                      <button
                        onClick={() => {
                          handleAdminLogout();
                          setIsOpen(false);
                        }}
                        className="flex items-center text-gray-600 hover:text-gray-800 font-medium transition-all px-3 py-2"
                        title="Logout Admin"
                      >
                        <FiLogOut size={16} />
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => {
                        setShowAdminLogin(true);
                        setIsOpen(false);
                      }}
                      className="flex items-center text-crimson-600 hover:text-crimson-700 font-semibold transition-all px-3 py-2 w-full"
                    >
                      <FiShield className="mr-2" size={16} />
                      Admin Access
                    </button>
                  )}

                  <Link
                    to="/join"
                    className="block w-full text-center py-2.5 bg-crimson-500 hover:bg-crimson-600 text-white rounded-lg font-semibold shadow-lg"
                    onClick={() => setIsOpen(false)}
                  >
                    JOIN NOW
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>
    </>
  );
};

export default Navbar; 