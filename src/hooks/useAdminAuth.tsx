import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface AdminAuthContextType {
  isAdminAuthenticated: boolean;
  authenticateAdmin: () => void;
  logoutAdmin: () => void;
  checkAdminSession: () => boolean;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

interface AdminAuthProviderProps {
  children: ReactNode;
}

export const AdminAuthProvider = ({ children }: AdminAuthProviderProps) => {
  const [isAdminAuthenticated, setIsAdminAuthenticated] = useState(false);

  // Session duration: 2 hours
  const SESSION_DURATION = 2 * 60 * 60 * 1000; // 2 hours in milliseconds

  const checkAdminSession = (): boolean => {
    const adminAccess = localStorage.getItem('adminAccess');
    const adminAccessTime = localStorage.getItem('adminAccessTime');
    
    if (!adminAccess || !adminAccessTime) {
      return false;
    }

    const accessTime = parseInt(adminAccessTime);
    const currentTime = Date.now();
    
    // Check if session has expired
    if (currentTime - accessTime > SESSION_DURATION) {
      // Session expired, clear it
      localStorage.removeItem('adminAccess');
      localStorage.removeItem('adminAccessTime');
      return false;
    }

    return adminAccess === 'true';
  };

  const authenticateAdmin = () => {
    setIsAdminAuthenticated(true);
    // The localStorage is set in the AdminLogin component
  };

  const logoutAdmin = () => {
    setIsAdminAuthenticated(false);
    localStorage.removeItem('adminAccess');
    localStorage.removeItem('adminAccessTime');
  };

  // Check for existing admin session on mount
  useEffect(() => {
    const hasValidSession = checkAdminSession();
    setIsAdminAuthenticated(hasValidSession);
  }, []);

  // Auto-logout when session expires
  useEffect(() => {
    if (isAdminAuthenticated) {
      const checkInterval = setInterval(() => {
        const hasValidSession = checkAdminSession();
        if (!hasValidSession) {
          setIsAdminAuthenticated(false);
        }
      }, 60000); // Check every minute

      return () => clearInterval(checkInterval);
    }
  }, [isAdminAuthenticated]);

  const value = {
    isAdminAuthenticated,
    authenticateAdmin,
    logoutAdmin,
    checkAdminSession
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};

export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};
