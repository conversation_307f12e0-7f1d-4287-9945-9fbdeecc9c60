import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import type { Database } from '../lib/database.types';
import MemberCard from '../components/MemberCard';
import { motion } from 'framer-motion';
import { FiSearch, FiFilter, FiUsers, FiUserPlus } from 'react-icons/fi';

type Member = Database['public']['Tables']['members']['Row'];

export default function Directory() {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedYear, setSelectedYear] = useState<string>('all');

  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const { data, error } = await supabase
          .from('members')
          .select('*')
          .order('name');

        if (error) throw error;
        setMembers(data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch members');
        console.error('Error fetching members:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, []);

  const graduationYears = [...new Set(members.map(m => m.graduation_year))].sort((a, b) => b - a);

  const filteredMembers = members.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (member.profession || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesYear = selectedYear === 'all' || member.graduation_year.toString() === selectedYear;
    return matchesSearch && matchesYear;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-beige-50 to-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#7D0E17] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-lg text-[#7D0E17] font-medium">Loading directory...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-beige-50 to-white flex items-center justify-center p-4">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <p className="text-xl text-[#7D0E17] font-bold mb-2">Error Loading Directory</p>
          <p className="text-gray-600 mb-6">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 bg-[#7D0E17] text-white rounded-md hover:bg-[#590A11] transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gray-50"
    >
      {/* Modern Header Section */}
      <div className="relative bg-gradient-to-r from-[#7D0E17] to-[#590A11] py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute right-0 top-0 w-1/2 h-full bg-white/5 transform skew-x-12"></div>
          <div className="absolute left-0 bottom-0 w-1/3 h-2/3 bg-black/10 transform -skew-x-12"></div>
          <div className="absolute right-1/4 top-1/4 w-48 h-48 bg-white/10 rounded-full blur-2xl"></div>
          <div className="absolute left-1/4 bottom-1/4 w-64 h-64 bg-black/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-5xl md:text-6xl font-bold text-white mb-6"
            >
              Alumni Directory
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-gray-200 leading-relaxed"
            >
              Connect with fellow Palm Beach Lakes graduates and expand your professional network
            </motion.p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-16 container-custom">
        {members.length === 0 ? (
          <motion.div 
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-16 bg-white rounded-2xl shadow-xl border border-gray-100 max-w-2xl mx-auto"
          >
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FiUsers className="w-10 h-10 text-[#7D0E17]" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">No Alumni Listed Yet</h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Be the first to join our alumni directory and help us build our community!
            </p>
            <Link
              to="/join"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#7D0E17] to-[#590A11] text-white rounded-full hover:from-[#590A11] hover:to-[#7D0E17] transition-all transform hover:scale-105 shadow-lg hover:shadow-xl text-lg font-medium"
            >
              <FiUserPlus className="mr-2" />
              Join the Directory
            </Link>
          </motion.div>
        ) : (
          <>
            {/* Search and Filter Section */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-12 max-w-4xl mx-auto"
            >
              <div className="flex flex-col md:flex-row gap-4">
                {/* Search Input */}
                <div className="flex-1 relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by name or profession..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#7D0E17] focus:border-transparent transition-all"
                  />
                </div>

                {/* Year Filter */}
                <div className="relative">
                  <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(e.target.value)}
                    className="pl-10 pr-8 py-3 border border-gray-200 rounded-lg appearance-none bg-white focus:ring-2 focus:ring-[#7D0E17] focus:border-transparent transition-all min-w-[180px]"
                  >
                    <option value="all">All Years</option>
                    {graduationYears.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
              </div>
            </motion.div>

            {/* Directory Grid */}
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {filteredMembers.map((member, index) => (
                <motion.div
                  key={member.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <MemberCard member={member} />
                </motion.div>
              ))}
            </motion.div>

            {filteredMembers.length === 0 && (
              <div className="text-center py-12 bg-white rounded-xl shadow-sm">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FiSearch className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-lg text-gray-600">No alumni found matching your search criteria.</p>
              </div>
            )}

            {/* Join Directory Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="mt-16 relative overflow-hidden"
            >
              <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl border border-gray-200 p-12 text-center">
                <div className="max-w-2xl mx-auto">
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">Join Our Alumni Community</h3>
                  <p className="text-lg text-gray-600 mb-8">
                    Are you a Palm Beach Lakes graduate? Join our growing alumni network and stay connected!
                  </p>
                  <Link
                    to="/join"
                    className="inline-flex items-center px-8 py-4 bg-[#7D0E17] text-white rounded-lg hover:bg-[#590A11] transition-colors shadow-lg hover:shadow-xl"
                  >
                    <FiUserPlus className="mr-2" />
                    Join the Directory
                  </Link>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </div>
    </motion.div>
  );
} 