import { motion } from 'framer-motion';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaEnvelope, FaArrowRight, FaPlay } from 'react-icons/fa';

const About = () => {
  const executiveBoard = [
    { 
      name: "<PERSON>", 
      position: "President",
      title: "Alumni Association President",
      education: "Class of [Year] • [Degree/Field]",
      bio: "<PERSON> brings exceptional leadership and vision to the Palm Beach Lakes Alumni Association. He brings extensive experience coaching high school students and mentoring youth, with a focus on leadership, discipline, and personal growth. His work reflects a deep dedication to fostering meaningful connections among alumni and empowering future generations.",
      image: "/images/members/jeff-evers.png",
      hasPhoto: true
    },
    { 
      name: "<PERSON>", 
      position: "Vice-President",
      title: "Alumni Association Vice-President",
      education: "Class of [Year] • [Degree/Field]", 
      bio: "<PERSON> serves as a cornerstone of leadership within our organization, bringing years of professional experience and unwavering commitment to alumni engagement.",
      image: "/images/members/al-shipman.jpg",
      hasPhoto: true
    },
    { 
      name: "<PERSON><PERSON>", 
      position: "Treasurer",
      title: "Alumni Association Treasurer",
      education: "Class of [Year] • [Degree/Field]",
      bio: "<PERSON><PERSON>ffie oversees the financial stewardship of our organization with meticulous attention to detail and fiscal responsibility.",
      image: "/images/members/clover-coffie.png",
      hasPhoto: true
    },
    { 
      name: "Tara <PERSON>", 
      position: "Assistant Treasurer",
      title: "Alumni Association Assistant Treasurer", 
      education: "Class of [Year] • [Degree/Field]",
      bio: "Tara Wallace provides essential financial support and oversight, working closely with our treasurer to maintain transparent and effective financial operations.",
      image: "/images/members/tara-wallace.jpg",
      hasPhoto: true
    },
    { 
      name: "Vinny Sutherland", 
      position: "Chair",
      title: "Alumni Association Chair",
      education: "Class of [Year] • [Degree/Field]",
      bio: "Vinny Sutherland chairs critical initiatives within our association, bringing innovative ideas and collaborative leadership to advance our mission.",
      image: "/images/board/vinny-sutherland.jpg",
      hasPhoto: false
    },
    { 
      name: "Adriana Ortiz-Coffie", 
      position: "Chair", 
      title: "Alumni Association Chair",
      education: "Class of [Year] • [Degree/Field]",
      bio: "Adriana Ortiz-Coffie leads with passion and dedication, focusing on expanding our reach and enhancing the alumni experience.",
      image: "/images/members/adriana-example.png",
      hasPhoto: true
    },
    { 
      name: "Nicole Dunbar", 
      position: "Secretary",
      title: "Alumni Association Secretary",
      education: "Class of [Year] • [Degree/Field]",
      bio: "Nicole Dunbar maintains the organizational backbone of our association, ensuring effective communication and documentation of our initiatives.",
      image: "/images/board/nicole-dunbar.jpg",
      hasPhoto: false
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gray-50 pt-26 md:pt-30"
    >
      {/* Modern Hero Section */}
      <div id="our-story" className="relative bg-white">
        <div className="container-custom py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="inline-flex items-center px-4 py-2 bg-crimson-50 text-crimson-600 rounded-full text-sm font-medium">
                ✨ Est. 2025 • Building Legacies
              </div>
              <h1 className="text-5xl lg:text-7xl font-bold tracking-tight text-gray-900">
                About Our
                <span className="text-crimson-600 block">Legacy</span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                The Palm Beach Lakes Alumni Association unites generations of graduates to support our school community through meaningful connections and transformative impact.
              </p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="inline-flex items-center px-8 py-4 bg-crimson-600 text-white rounded-2xl font-semibold hover:bg-crimson-700 transition-colors shadow-lg"
              >
                Learn More <FaArrowRight className="ml-2" />
              </motion.button>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="aspect-square rounded-3xl bg-gradient-to-br from-crimson-500 to-crimson-700 p-1">
                <div className="w-full h-full rounded-3xl bg-white p-8 flex items-center justify-center">
                  <img 
                    src="/images/logo/palm-beach-lakes-picture.jpg" 
                    alt="Palm Beach Lakes Community High School" 
                    className="w-full h-full object-cover rounded-2xl"
                  />
                </div>
              </div>
              <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-2xl shadow-xl">
                <div className="text-center">
                  <div className="text-2xl font-bold text-crimson-600">500+</div>
                  <div className="text-sm text-gray-600">Alumni Strong</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Mission Statement */}
      <div className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center space-y-8"
          >
            <h2 className="text-4xl lg:text-6xl font-bold text-gray-900">
              Our <span className="text-crimson-600">Mission</span>
            </h2>
            <p className="text-2xl text-gray-700 leading-relaxed">
              To build <strong className="text-crimson-600">transformative bridges</strong> between successful alumni and aspiring students through mentorship, scholarships, and lifelong connections.
            </p>
            <div className="flex items-center justify-center pt-8">
              <motion.button
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-full font-medium hover:bg-gray-200 transition-colors"
              >
                <FaPlay className="mr-2" /> Watch Our Story
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Executive Board */}
      <div id="leadership" className="bg-white py-20">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Meet Our <span className="text-crimson-600">Leaders</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Visionary professionals dedicated to advancing our mission and strengthening our community bonds.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {executiveBoard.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group cursor-pointer"
              >
                <div className="bg-white rounded-3xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 border border-gray-100">
                  {/* Photo */}
                  <div className="aspect-square overflow-hidden bg-gray-100">
                    {member.hasPhoto ? (
                      <img 
                        src={member.image} 
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-20 h-20 bg-crimson-600 rounded-full flex items-center justify-center mb-4 mx-auto">
                            <FaUserTie className="text-white text-2xl" />
                          </div>
                          <p className="text-gray-500 font-medium">Photo Coming Soon</p>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Content */}
                  <div className="p-8">
                    <div className="mb-4">
                      <h3 className="text-2xl font-bold text-gray-900 mb-1 group-hover:text-crimson-600 transition-colors">
                        {member.name}
                      </h3>
                      <p className="text-crimson-600 font-semibold text-lg">
                        {member.position}
                      </p>
                    </div>
                    
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {member.bio}
                    </p>
                    
                    {/* Social Links */}
                    <div className="flex gap-3">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-crimson-600 hover:text-white transition-colors"
                      >
                        <FaLinkedin className="text-sm" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-crimson-600 hover:text-white transition-colors"
                      >
                        <FaEnvelope className="text-sm" />
                      </motion.button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default About; 