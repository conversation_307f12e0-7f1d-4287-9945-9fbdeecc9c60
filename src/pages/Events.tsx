import { motion } from 'framer-motion';
import { FaCalendarAlt, FaMapMarkerAlt, FaClock, FaSearch } from 'react-icons/fa';
import { Link } from 'react-router-dom';

const Events = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gray-50"
    >
      {/* Hero Section with Decorative Elements */}
      <div className="relative overflow-hidden bg-gradient-to-r from-[#7D0E17] to-[#590A11] text-white">
        <div className="absolute inset-0">
          <div className="absolute right-0 top-0 w-1/2 h-full bg-white/5 transform skew-x-12"></div>
          <div className="absolute left-0 bottom-0 w-1/3 h-2/3 bg-black/10 transform -skew-x-12"></div>
          <div className="absolute right-1/4 top-1/4 w-48 h-48 bg-white/10 rounded-full blur-2xl"></div>
          <div className="absolute left-1/4 bottom-1/4 w-64 h-64 bg-black/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative container-custom py-24">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-5xl md:text-6xl font-bold mb-8"
            >
              Events & Reunions
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-gray-200 leading-relaxed mb-12"
            >
              Connect with fellow alumni and support our school community through these upcoming gatherings
            </motion.p>
            
            {/* Search Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="max-w-2xl mx-auto"
            >
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search events..."
                  className="w-full px-6 py-4 rounded-full bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-white/30 backdrop-blur-sm"
                />
                <FaSearch className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-300" />
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-16 container-custom">
        <div className="max-w-7xl mx-auto">
          {/* Event Categories */}
          <div className="flex flex-wrap gap-4 justify-center mb-12">
            {['All Events', 'Reunions', 'Fundraisers', 'Networking', 'Community'].map((category) => (
              <button
                key={category}
                className="px-6 py-2 rounded-full bg-white border border-gray-200 text-gray-700 hover:bg-[#7D0E17] hover:text-white hover:border-transparent transition-all duration-300 shadow-sm"
              >
                {category}
              </button>
            ))}
          </div>

          {/* No Events State */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-center py-16"
          >
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FaCalendarAlt className="text-3xl text-gray-400" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">No Events Currently Listed</h2>
              <p className="text-gray-600 mb-8">
                Check back soon for upcoming events and reunions. We're constantly adding new opportunities to connect with your fellow alumni.
              </p>
              <div>
                <Link 
                  to="/contact"
                  className="inline-block w-full px-6 py-3 bg-[#7D0E17] text-white rounded-lg hover:bg-[#6B0C14] transition-colors shadow-lg hover:shadow-xl"
                >
                  Suggest an Event
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default Events; 