import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, Link } from 'react-router-dom';
import PageLayout from '../components/layout/PageLayout';
import { supabase } from '../lib/supabase';
import type { Database } from '../lib/database.types';

type Member = Database['public']['Tables']['members']['Insert'];

const Join = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [retryTimeoutId, setRetryTimeoutId] = useState<number | null>(null);

  useEffect(() => {
    // Clear retry timeout when component unmounts
    return () => {
      if (retryTimeoutId !== null) {
        window.clearTimeout(retryTimeoutId);
        setRetryTimeoutId(null);
      }
    };
  }, [retryTimeoutId]);

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Preview the image
    const reader = new FileReader();
    reader.onloadend = () => {
      setAvatarPreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    setAvatarFile(file);
  };

  const uploadAvatar = async (userId: string): Promise<string | null> => {
    if (!avatarFile) return null;

    try {
      console.log('Starting avatar upload for user:', userId);
      
      // Create a unique file name using just the user ID and file extension
      const fileExt = avatarFile.name.split('.').pop();
      const fileName = `${userId}.${fileExt}`;

      console.log('Uploading file:', { fileName, fileType: avatarFile.type, fileSize: avatarFile.size });

      // First, try to delete any existing avatar
      try {
        const { data: existingFiles } = await supabase
          .storage
          .from('member-photos')
          .list('', {
            search: userId
          });

        if (existingFiles && existingFiles.length > 0) {
          await Promise.all(
            existingFiles.map(file => 
              supabase.storage
                .from('member-photos')
                .remove([file.name])
            )
          );
        }
      } catch (err) {
        console.log('No existing avatar to delete or error deleting:', err);
      }

      // Upload the new file
      const { error: uploadError, data } = await supabase.storage
        .from('member-photos')
        .upload(fileName, avatarFile, {
          cacheControl: '0',
          upsert: true
        });

      if (uploadError) {
        console.error('Error uploading file:', uploadError);
        throw uploadError;
      }

      console.log('File uploaded successfully:', data);

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('member-photos')
        .getPublicUrl(fileName);

      console.log('Generated public URL:', publicUrl);
      
      // Verify the file exists
      const { data: checkFile } = await supabase.storage
        .from('member-photos')
        .list('', {
          search: fileName
        });

      if (!checkFile || checkFile.length === 0) {
        throw new Error('File upload succeeded but file not found in storage');
      }

      return publicUrl;
    } catch (err) {
      console.error('Error in uploadAvatar:', err);
      throw err; // Re-throw to handle in the calling function
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const formData = new FormData(event.currentTarget);
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;
      const name = formData.get('name') as string;
      const graduationYear = formData.get('graduationYear') as string;

      // Validate required fields
      const requiredFields = {
        name: name,
        email: email,
        password: password,
        graduationYear: graduationYear
      };

      const missingFields = Object.entries(requiredFields)
        .filter(([_, value]) => !value)
        .map(([key, _]) => key);

      if (missingFields.length > 0) {
        setError(`Please fill in all required fields: ${missingFields.join(', ')}`);
        setLoading(false);
        return;
      }

      // Validate graduation year
      const gradYear = parseInt(graduationYear);
      if (isNaN(gradYear) || gradYear < 1950 || gradYear > 2030) {
        setError('Please enter a valid graduation year between 1950 and 2030');
        setLoading(false);
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        setError('Please enter a valid email address');
        setLoading(false);
        return;
      }

      // Validate password length
      if (password.length < 8) {
        setError('Password must be at least 8 characters long');
        setLoading(false);
        return;
      }

      // Create auth user first
      console.log('Creating auth user...');
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name,
            graduation_year: parseInt(graduationYear)
          }
        }
      });

      if (authError) throw authError;
      if (!authData.user) throw new Error('Failed to create user');

      const userId = authData.user.id;
      console.log('Auth user created:', userId);

      // Handle avatar upload
      let avatarUrl = null;
      if (avatarFile) {
        try {
          console.log('Uploading avatar...');
          avatarUrl = await uploadAvatar(userId);
          console.log('Avatar uploaded successfully:', avatarUrl);
        } catch (uploadError) {
          console.error('Avatar upload failed:', uploadError);
          setError('Profile created but avatar upload failed. You can add a photo later from your profile.');
          // Continue with profile creation even if avatar upload fails
        }
      }

      // Create member profile
      const memberData: Member = {
        name: name,
        email: email,
        graduation_year: parseInt(graduationYear),
        profession: formData.get('profession') as string || null,
        bio: formData.get('bio') as string || null,
        avatar_url: avatarUrl,
        linkedin: formData.get('linkedin') as string || null,
        location: formData.get('location') as string || null,
        preferred_contact: [],
        interests: [],
        event_types: [],
        leadership_interest: false,
        skills_and_resources: null,
        wishes_to_see: null,
        additional_comments: null
      };

      console.log('Creating member profile...', { userId, memberData });

      const { error: profileError } = await supabase
        .from('members')
        .insert([{
          ...memberData,
          id: userId,
          created_at: new Date().toISOString()
        }]);

      if (profileError) throw profileError;

      // Navigate to success page
      navigate('/join/success');
    } catch (err: any) {
      console.error('Join error:', err);
      if (err.message?.includes('already exists')) {
        setError('An account with this email already exists. Please try signing in instead.');
      } else {
        setError(err.message || 'Failed to create account');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageLayout
      title="Join the Alumni Association"
      subtitle="Connect with fellow Palm Beach Lakes graduates and help us build our growing network."
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="max-w-6xl mx-auto space-y-8"
      >
        {/* Directions - Prominently displayed at top */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-r from-[#7D0E17]/10 to-[#7D0E17]/5 border-2 border-[#7D0E17]/20 rounded-2xl p-8 shadow-lg"
        >
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-[#7D0E17] text-white rounded-full mb-6">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7 12a5 5 0 1110 0 7 7 0 11-10 0z" />
              </svg>
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Complete Both Steps to Join Our Community
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto mb-6">
              <div className="bg-white/70 rounded-xl p-6 border border-[#7D0E17]/10">
                <div className="flex items-center justify-center w-10 h-10 bg-[#7D0E17] text-white rounded-full mx-auto mb-3 text-lg font-bold">
                  1
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Fill Out Questionnaire</h3>
                <p className="text-sm text-gray-600">
                  Share your interests, preferences, and how you'd like to contribute to our community
                </p>
              </div>
              
              <div className="bg-white/70 rounded-xl p-6 border border-[#7D0E17]/10">
                <div className="flex items-center justify-center w-10 h-10 bg-[#7D0E17] text-white rounded-full mx-auto mb-3 text-lg font-bold">
                  2
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Create Directory Profile</h3>
                <p className="text-sm text-gray-600">
                  Add your profile to our alumni directory so others can find and connect with you
                </p>
              </div>
            </div>
            
            <p className="text-sm text-gray-600">
              By joining, you agree to receive communications from the Palm Beach Lakes Alumni Association. 
              You can unsubscribe at any time.
            </p>
          </div>
        </motion.div>

        {/* Step 1: Questionnaire */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white/90 backdrop-blur-lg rounded-2xl p-8 shadow-xl"
        >
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-14 h-14 bg-[#7D0E17] text-white rounded-full font-bold text-2xl mb-6 shadow-lg">
              1
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Tell Us About Your Interests
            </h3>
            <div className="max-w-3xl mx-auto">
              <p className="text-lg text-gray-600 leading-relaxed">
                Fill out our comprehensive questionnaire to help us understand how you'd like to be involved. 
                Your responses will be sent directly to our team at{' '}
                <span className="font-medium text-[#7D0E17]"><EMAIL></span>.
              </p>
            </div>
          </div>

          {/* Google Form Embed with improved error handling */}
          <div className="w-full relative">
            <div className="w-full h-[1200px] bg-white">
              <iframe
                src="https://docs.google.com/forms/d/e/1FAIpQLSe6sqvkRORjn_m7k7Ks9BneQvSRl6VK-ToG2dysB19LgOstcQ/viewform?embedded=true"
                width="100%"
                height="100%"
                frameBorder="0"
                marginHeight={0}
                marginWidth={0}
                className="rounded-lg shadow-inner absolute inset-0"
                title="Alumni Registration Form"
                onError={(e) => {
                  const iframe = e.target as HTMLIFrameElement;
                  iframe.style.display = 'none';
                  const fallback = iframe.parentElement?.querySelector('.fallback');
                  if (fallback) {
                    fallback.classList.remove('hidden');
                  }
                }}
              >
                Loading…
              </iframe>
              
              {/* Enhanced Fallback for when iframe doesn't work */}
              <div className="fallback hidden absolute inset-0 flex flex-col items-center justify-center p-6 bg-gray-50 rounded-xl border border-gray-200">
                <svg className="w-16 h-16 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Unable to Load Form</h3>
                <p className="text-gray-600 mb-6 text-center max-w-md">
                  The form may be blocked by your browser or extensions. Please try opening it in a new tab or temporarily disable any ad blockers.
                </p>
                <a
                  href="https://docs.google.com/forms/d/e/1FAIpQLSe6sqvkRORjn_m7k7Ks9BneQvSRl6VK-ToG2dysB19LgOstcQ/viewform"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-6 py-3 bg-[#7D0E17] text-white rounded-lg hover:bg-[#6B0C14] transition-colors font-medium shadow-lg hover:shadow-xl"
                >
                  Open Form in New Tab
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Step 2: Create Directory Profile */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white/90 backdrop-blur-lg rounded-2xl p-8 shadow-xl"
        >
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-14 h-14 bg-[#7D0E17] text-white rounded-full font-bold text-2xl mb-6 shadow-lg">
              2
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Create Your Directory Profile
            </h3>
            <div className="max-w-3xl mx-auto">
              <p className="text-lg text-gray-600 leading-relaxed">
                Create a profile that will appear in our alumni directory so other graduates can find and connect with you.
                Add a photo and share some details about yourself.
              </p>
            </div>
          </div>

          <div className="max-w-2xl mx-auto">
            {error && (
              <div className={`p-4 rounded-lg ${
                error.includes('already exists') 
                  ? 'bg-yellow-50 text-yellow-800 border border-yellow-200'
                  : error.includes('Please wait') 
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'bg-red-100 text-red-700 border border-red-200'
              }`}>
                <p className="font-medium">
                  {error.includes('already exists') 
                    ? 'Account Exists'
                    : error.includes('Please wait')
                      ? 'Rate Limit Reached'
                      : 'Error'}
                </p>
                <p className="mt-1 text-sm">{error}</p>
                {error.includes('already exists') && (
                  <div className="mt-2">
                    <Link 
                      to="/signin" 
                      className="text-yellow-900 font-medium hover:text-yellow-800 underline"
                    >
                      Sign in here
                    </Link>
                  </div>
                )}
                {error.includes('Please wait') && (
                  <div className="mt-2 h-1 bg-blue-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-600 transition-all duration-1000"
                      style={{
                        width: '100%',
                        animation: `shrink ${error.match(/\d+/)?.[0] || 60}s linear forwards`
                      }}
                    />
                  </div>
                )}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Photo Upload */}
              <div className="text-center">
                <div className="mb-4">
                  {avatarPreview ? (
                    <img
                      src={avatarPreview}
                      alt="Profile preview"
                      className="w-32 h-32 rounded-full mx-auto object-cover border-4 border-white shadow-lg"
                    />
                  ) : (
                    <div className="w-32 h-32 rounded-full mx-auto bg-gray-200 flex items-center justify-center">
                      <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  )}
                </div>
                
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Profile Photo
                </label>
                <div className="flex items-center justify-center">
                  <label className="cursor-pointer bg-white px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-crimson-500">
                    <span>Upload a photo</span>
                    <input
                      type="file"
                      className="sr-only"
                      accept="image/*"
                      onChange={handleAvatarChange}
                    />
                  </label>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  PNG, JPG, GIF up to 10MB
                </p>
              </div>

              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Full Name <span className="text-red-600">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email <span className="text-red-600">*</span>
                </label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter your email address"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password <span className="text-red-600">*</span>
                </label>
                <input
                  type="password"
                  name="password"
                  id="password"
                  required
                  minLength={8}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Minimum 8 characters"
                />
              </div>

              <div>
                <label htmlFor="graduationYear" className="block text-sm font-medium text-gray-700">
                  Graduation Year <span className="text-red-600">*</span>
                </label>
                <input
                  type="number"
                  name="graduationYear"
                  id="graduationYear"
                  required
                  min="1950"
                  max="2030"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter your graduation year"
                />
              </div>

              <div>
                <label htmlFor="profession" className="block text-sm font-medium text-gray-700">
                  Profession
                </label>
                <input
                  type="text"
                  name="profession"
                  id="profession"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Optional: Enter your profession"
                />
              </div>

              <div>
                <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                  Bio
                </label>
                <textarea
                  name="bio"
                  id="bio"
                  rows={4}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Optional: Tell us about yourself"
                ></textarea>
              </div>

              <div>
                <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700">
                  LinkedIn Profile URL
                </label>
                <input
                  type="url"
                  name="linkedin"
                  id="linkedin"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Optional: Your LinkedIn profile URL"
                />
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                  Location
                </label>
                <input
                  type="text"
                  name="location"
                  id="location"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Optional: Your current location"
                />
              </div>

              {/* Add a note about required fields */}
              <div className="text-sm text-gray-500 mt-4">
                <p><span className="text-red-600">*</span> Required fields</p>
                <ul className="list-disc list-inside mt-2">
                  <li>Full Name</li>
                  <li>Email Address</li>
                  <li>Password (minimum 8 characters)</li>
                  <li>Graduation Year (between 1950-2030)</li>
                </ul>
              </div>

              <button
                type="submit"
                disabled={loading || (error?.includes('Please wait') ?? false)}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating Account...' : error?.includes('Please wait') ? 'Please Wait...' : 'Join Now'}
              </button>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </PageLayout>
  );
};

export default Join; 