import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { galleryApi, type GalleryItem } from '../lib/api';
import { FaCamera, FaUpload } from 'react-icons/fa';

const Gallery = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [loading, setLoading] = useState(true);

  // Gallery categories
  const categories = [
    { id: 'all', name: 'All Photos' },
    { id: 'reunions', name: 'Reunions' },
    { id: 'sports', name: 'Sports' },
    { id: 'campus', name: 'Campus' },
    { id: 'events', name: 'Events' },
    { id: 'achievements', name: 'Achievements' },
    { id: 'historical', name: 'Historical' },
  ];

  useEffect(() => {
    loadGalleryItems();
  }, []);

  const loadGalleryItems = async () => {
    try {
      const items = await galleryApi.getAll();
      setGalleryItems(items);
    } catch (error) {
      console.error('Error loading gallery items:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = activeFilter === 'all'
    ? galleryItems
    : galleryItems.filter(item => item.category === activeFilter);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gray-50"
    >
      {/* Modern Header Section */}
      <div className="relative bg-gradient-to-r from-[#7D0E17] to-[#590A11] py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute right-0 top-0 w-1/2 h-full bg-white/5 transform skew-x-12"></div>
          <div className="absolute left-0 bottom-0 w-1/3 h-2/3 bg-black/10 transform -skew-x-12"></div>
          <div className="absolute right-1/4 top-1/4 w-48 h-48 bg-white/10 rounded-full blur-2xl"></div>
          <div className="absolute left-1/4 bottom-1/4 w-64 h-64 bg-black/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-5xl md:text-6xl font-bold text-white mb-6"
            >
              Photo Gallery
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-gray-200 leading-relaxed"
            >
              Explore our collection of memories and moments from throughout the years
            </motion.p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-16 container-custom">
        {/* Category filters */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map(category => {
            const count = category.id === 'all'
              ? galleryItems.length
              : galleryItems.filter(item => item.category === category.id).length;

            return (
              <motion.button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`px-6 py-3 rounded-full transition-all duration-300 ${
                  activeFilter === category.id
                    ? 'bg-[#7D0E17] text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-50 shadow-sm'
                }`}
              >
                {category.name} ({count})
              </motion.button>
            );
          })}
        </div>
          
        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#7D0E17]"></div>
          </div>
        )}

        {/* Gallery Grid */}
        {!loading && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredItems.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="group relative overflow-hidden rounded-xl bg-white shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="aspect-w-4 aspect-h-3 overflow-hidden">
                  <img
                    src={item.image_url}
                    alt={item.title}
                    className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = `
                          <div class="w-full h-full bg-gradient-to-br from-[#7D0E17]/60 to-[#7D0E17]/80 flex items-center justify-center">
                            <span class="text-white text-opacity-90 text-lg">Image Not Available</span>
                          </div>
                        `;
                      }
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-6 group-hover:translate-y-0 transition-transform duration-300">
                  <div className="flex justify-between items-start mb-2">
                    <div className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm">
                      {item.year}
                    </div>
                    {item.is_featured && (
                      <span className="bg-yellow-400 text-yellow-900 text-xs px-3 py-1 rounded-full">
                        Featured
                      </span>
                    )}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                  <p className="text-white/90 text-sm line-clamp-2">
                    {item.description}
                  </p>
                  {item.photographer && (
                    <p className="text-white/70 text-xs mt-2">
                      Photo by: {item.photographer}
                    </p>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredItems.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16 bg-white rounded-2xl shadow-sm"
          >
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaCamera className="text-3xl text-gray-400" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">No Photos Found</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              {activeFilter === 'all'
                ? 'No photos have been added to the gallery yet.'
                : `No photos found in the ${categories.find(c => c.id === activeFilter)?.name.toLowerCase()} category.`
              }
            </p>
            <button className="inline-flex items-center px-6 py-3 bg-[#7D0E17] text-white rounded-lg hover:bg-[#6B0C14] transition-colors shadow-lg hover:shadow-xl">
              <FaUpload className="mr-2" />
              Submit Photos
            </button>
          </motion.div>
        )}

        {/* Share Your Memories Section */}
        {filteredItems.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="mt-16 relative overflow-hidden"
          >
            <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl border border-gray-200 p-12 text-center">
              <div className="max-w-2xl mx-auto">
                <h3 className="text-3xl font-bold text-gray-900 mb-4">Share Your Memories</h3>
                <p className="text-lg text-gray-600 mb-8">
                  Do you have photos from your time at Palm Beach Lakes or from alumni events? 
                  Help us grow our collection!
                </p>
                <button className="inline-flex items-center px-8 py-4 bg-[#7D0E17] text-white rounded-lg hover:bg-[#6B0C14] transition-colors shadow-lg hover:shadow-xl">
                  <FaUpload className="mr-2" />
                  Submit Your Photos
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default Gallery; 