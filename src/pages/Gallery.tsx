import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import HeroSection from '../components/layout/HeroSection';
import { galleryApi, type GalleryItem } from '../lib/api';

const Gallery = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [loading, setLoading] = useState(true);

  // Gallery categories
  const categories = [
    { id: 'all', name: 'All Photos' },
    { id: 'reunions', name: 'Reunions' },
    { id: 'sports', name: 'Sports' },
    { id: 'campus', name: 'Campus' },
    { id: 'events', name: 'Events' },
    { id: 'achievements', name: 'Achievements' },
    { id: 'historical', name: 'Historical' },
  ];

  useEffect(() => {
    loadGalleryItems();
  }, []);

  const loadGalleryItems = async () => {
    try {
      const items = await galleryApi.getAll();
      setGalleryItems(items);
    } catch (error) {
      console.error('Error loading gallery items:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = activeFilter === 'all'
    ? galleryItems
    : galleryItems.filter(item => item.category === activeFilter);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <HeroSection
        title="Alumni Memories"
        subtitle="A visual journey through the history and special moments of Palm Beach Lakes"
      />

      {/* Main content */}
      <div className="py-16 container-custom">
        <div className="mb-12">
          <h2 className="font-display text-3xl font-bold mb-8 text-center">Photo Gallery</h2>
          
          {/* Category filters */}
          <div className="flex flex-wrap justify-center gap-2 mb-12">
            {categories.map(category => {
              const count = category.id === 'all'
                ? galleryItems.length
                : galleryItems.filter(item => item.category === category.id).length;

              return (
                <button
                  key={category.id}
                  onClick={() => setActiveFilter(category.id)}
                  className={`px-4 py-2 rounded-full transition-all ${
                    activeFilter === category.id
                      ? 'bg-crimson-700 text-white'
                      : 'bg-beige-100 text-gray-700 hover:bg-beige-200'
                  }`}
                >
                  {category.name} ({count})
                </button>
              );
            })}
          </div>
          
          {/* Loading State */}
          {loading && (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-crimson-700"></div>
            </div>
          )}

          {/* Gallery grid */}
          {!loading && (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredItems.map((item) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="photo-memory group hover:scale-[1.02] transition-transform duration-300 relative overflow-hidden rounded-lg shadow-lg"
                >
                  <div className="aspect-w-4 aspect-h-3">
                    <img
                      src={item.image_url}
                      alt={item.title}
                      className="w-full h-64 object-cover"
                      onError={(e) => {
                        // Fallback to placeholder if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const parent = target.parentElement;
                        if (parent) {
                          parent.innerHTML = `
                            <div class="w-full h-64 bg-gradient-to-br from-crimson-500 to-crimson-800 flex items-center justify-center">
                              <span class="text-white text-opacity-70 text-lg">Image Not Available</span>
                            </div>
                          `;
                        }
                      }}
                    />
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="year-marker inline-block">{item.year}</div>
                      {item.is_featured && (
                        <span className="bg-yellow-400 text-yellow-900 text-xs px-2 py-1 rounded-full">
                          Featured
                        </span>
                      )}
                    </div>
                    <h3 className="text-white text-lg font-bold">{item.title}</h3>
                    <p className="text-white/80 text-sm mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      {item.description}
                    </p>
                    {item.photographer && (
                      <p className="text-white/60 text-xs mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        Photo by: {item.photographer}
                      </p>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!loading && filteredItems.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No photos found</h3>
              <p className="text-gray-600">
                {activeFilter === 'all'
                  ? 'No photos have been added to the gallery yet.'
                  : `No photos found in the ${categories.find(c => c.id === activeFilter)?.name.toLowerCase()} category.`
                }
              </p>
            </div>
          )}
        </div>

        {/* Call to contribute */}
        <div className="mt-16 text-center max-w-2xl mx-auto">
          <div className="bg-beige-50 rounded-xl p-8 border border-beige-100">
            <h3 className="font-display text-2xl font-bold mb-4">Share Your Memories</h3>
            <p className="text-gray-700 mb-6">
              Do you have photos from your time at Palm Beach Lakes or from alumni events? 
              We'd love to include them in our gallery!
            </p>
            <button className="btn btn-primary">
              Submit Your Photos
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default Gallery; 