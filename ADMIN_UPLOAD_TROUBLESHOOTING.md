# Admin Image Upload Troubleshooting Guide

## Problem Identified
The admin dashboard image uploads are failing because there's a mismatch between the frontend authentication (localStorage-based) and the backend storage policies (Supabase auth-based).

## Root Cause
1. **Authentication Mismatch**: Admin dashboard uses localStorage for session management
2. **Missing Admin User**: No actual Supabase user with admin privileges exists
3. **Storage Policy Requirements**: Supabase storage policies require authenticated users with admin role metadata

## Solution Implemented

### 1. Updated Upload Function
- Modified `uploadFile()` in `src/lib/api.ts` to automatically authenticate as admin user before uploads
- Added `authenticateAdmin()` function that signs in with admin credentials

### 2. Enhanced Error Handling
- Added detailed console logging to track upload progress
- Better error messages for debugging
- Graceful fallback handling

## Setup Instructions

### Step 1: Create Admin User in Supabase
1. Go to your Supabase dashboard
2. Navigate to **Authentication > Users**
3. Click **"Add user"**
4. Create user with:
   - **Email**: `<EMAIL>`
   - **Password**: `Rams1989!`
   - **Email Confirm**: Check this box
5. Click **"Create user"**

### Step 2: Run SQL Setup
1. Go to **SQL Editor** in your Supabase dashboard
2. Copy and paste the contents of `admin-user-setup.sql`
3. Click **"Run"** to execute the SQL commands
4. Verify the admin user has the correct role by checking the output

### Step 3: Test the Upload
1. Access the admin dashboard at `/admin`
2. Enter password: `Rams1989!`
3. Try uploading an image in Events or Gallery management
4. Check browser console for detailed logs

## Debugging Steps

### Check Browser Console
Open browser developer tools (F12) and look for:
- `Creating event with data:` - Confirms form submission
- `Uploading event image...` - Confirms upload attempt
- `Image uploaded successfully:` - Confirms successful upload
- Any error messages with details

### Common Error Messages

**"Admin authentication failed"**
- Solution: Ensure admin user exists in Supabase Auth
- Run the SQL setup script again

**"Storage error: new row violates row-level security policy"**
- Solution: Admin user doesn't have proper role metadata
- Check the SQL script ran successfully

**"Network connection failed"**
- Solution: Check internet connection and Supabase project status

### Verify Admin User Setup
Run this query in Supabase SQL Editor:
```sql
SELECT
  id,
  email,
  raw_user_meta_data->>'role' as role,
  email_confirmed_at
FROM auth.users
WHERE email = '<EMAIL>';
```

Expected result:
- Email: `<EMAIL>`
- Role: `admin`
- Email confirmed: Should have a timestamp

### Check Storage Buckets
Verify buckets exist in **Storage** section:
- `event-images` (public)
- `gallery-images` (public)

### Test Upload Manually
1. Go to **Storage > event-images**
2. Try uploading a file manually
3. If this fails, check storage policies

## Fallback Behavior
If uploads still fail, the system will:
1. Create the event/gallery item without images
2. Show a warning message
3. Allow you to edit the item later to add images

## Alternative Solutions

### Option 1: Temporary Policy Bypass
If you need immediate functionality, you can temporarily make storage more permissive:

```sql
-- Temporary: Allow all authenticated users to upload
CREATE POLICY "Temp allow all uploads" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id IN ('event-images', 'gallery-images') AND
    auth.role() = 'authenticated'
  );
```

### Option 2: Manual File Management
1. Upload files directly to Supabase Storage
2. Copy the public URLs
3. Paste URLs into the image URL fields when creating events/gallery items

## Contact Support
If issues persist:
1. Check all console logs
2. Verify Supabase project is active
3. Ensure environment variables are correct
4. Try the manual upload test in Supabase dashboard

## Security Notes
- Admin credentials are hardcoded for simplicity
- In production, consider using environment variables
- The admin user has elevated privileges - protect access carefully
