-- Database setup for Palm Beach Lakes Alumni Association
-- Run these commands in your Supabase SQL editor

-- Create events table
CREATE TABLE IF NOT EXISTS events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  date TIMESTAMP WITH TIME ZONE NOT NULL,
  location TEXT NOT NULL,
  image_url TEXT,
  flyer_url TEXT,
  category TEXT,
  is_featured BOOLEAN DEFAULT FALSE
);

-- Create gallery_items table
CREATE TABLE IF NOT EXISTS gallery_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  title TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  year TEXT,
  image_url TEXT NOT NULL,
  photographer TEXT,
  is_featured BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0
);

-- Create storage buckets for file uploads
INSERT INTO storage.buckets (id, name, public) 
VALUES 
  ('event-images', 'event-images', true),
  ('gallery-images', 'gallery-images', true)
ON CONFLICT (id) DO NOTHING;

-- Set up Row Level Security (RLS) policies

-- Events table policies
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Allow public read access to events
CREATE POLICY "Public can view events" ON events
  FOR SELECT USING (true);

-- Allow authenticated users with admin role to manage events
CREATE POLICY "Admins can manage events" ON events
  FOR ALL USING (
    auth.role() = 'authenticated' AND 
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

-- Gallery items table policies
ALTER TABLE gallery_items ENABLE ROW LEVEL SECURITY;

-- Allow public read access to gallery items
CREATE POLICY "Public can view gallery items" ON gallery_items
  FOR SELECT USING (true);

-- Allow authenticated users with admin role to manage gallery items
CREATE POLICY "Admins can manage gallery items" ON gallery_items
  FOR ALL USING (
    auth.role() = 'authenticated' AND 
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

-- Storage policies for event-images bucket
CREATE POLICY "Public can view event images" ON storage.objects
  FOR SELECT USING (bucket_id = 'event-images');

CREATE POLICY "Admins can upload event images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'event-images' AND
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

CREATE POLICY "Admins can update event images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'event-images' AND
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

CREATE POLICY "Admins can delete event images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'event-images' AND
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

-- Storage policies for gallery-images bucket
CREATE POLICY "Public can view gallery images" ON storage.objects
  FOR SELECT USING (bucket_id = 'gallery-images');

CREATE POLICY "Admins can upload gallery images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'gallery-images' AND
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

CREATE POLICY "Admins can update gallery images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'gallery-images' AND
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

CREATE POLICY "Admins can delete gallery images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'gallery-images' AND
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' = 'admin'
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_date ON events(date);
CREATE INDEX IF NOT EXISTS idx_events_category ON events(category);
CREATE INDEX IF NOT EXISTS idx_events_featured ON events(is_featured);

CREATE INDEX IF NOT EXISTS idx_gallery_category ON gallery_items(category);
CREATE INDEX IF NOT EXISTS idx_gallery_featured ON gallery_items(is_featured);
CREATE INDEX IF NOT EXISTS idx_gallery_sort_order ON gallery_items(sort_order);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_events_updated_at 
  BEFORE UPDATE ON events 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gallery_items_updated_at 
  BEFORE UPDATE ON gallery_items 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data (optional)
INSERT INTO events (title, description, date, location, category, is_featured) VALUES
  ('Annual Alumni Reunion 2024', 'Join us for our biggest reunion yet! Reconnect with classmates, enjoy great food, and celebrate our shared memories.', '2024-06-15 18:00:00+00', 'Palm Beach Convention Center', 'reunion', true),
  ('Scholarship Fundraising Gala', 'Help us raise funds for student scholarships. An elegant evening of dinner, dancing, and giving back.', '2024-04-20 19:00:00+00', 'The Breakers Hotel', 'fundraising', true),
  ('Alumni Networking Mixer', 'Casual networking event for recent graduates and established professionals.', '2024-03-10 17:30:00+00', 'Downtown West Palm Beach', 'networking', false);

-- Note: You'll need to add gallery items through the admin interface
-- since they require image uploads
